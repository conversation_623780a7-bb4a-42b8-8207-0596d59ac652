import ZnUser from '#models/zn_user'
import JwtService from '#services/jwt_service'
import { NewsfeedService } from '#services/newsfeed/newsfeed_service'
import { newsfeedValidator } from '#validators/newsfeed_validator'
import { HttpContext } from '@adonisjs/core/http'
import { CacheResponse } from '../decorators/cache_response.decorator.js'
import ZnPost, { EPostSource, EPostType } from '#models/zn_post'
import { MEDIA_TYPE } from '#constants/media'
import TranslationService from '../services/translation_service.js'
import { TranslationModelName } from '../services/translation_model_service.js'
import { RESOURCE_TYPE } from '#constants/like_comment_resource'
import db from '@adonisjs/lucid/services/db'
import { PostService } from '../services/post_service.js'

export default class NewsfeedController {
  private translationService
  private postService
  constructor() {
    this.translationService = new TranslationService()
    this.postService = new PostService()
  }
  /**
   * @list
   * @tag Newsfeed
   * @summary List newsfeed
   * @description List newsfeed descriptively
   * @paramQuery limit - Record Limit (default 10) - @type(number)
   * @paramQuery miles - miles - @type(number)
   * @paramQuery latitude - latitude - @type(number)
   * @paramQuery longitude - longitude - @type(number)
   * @requestBody {"filterIds":[],"locale":"en"}
   * @responseBody 200 - [{"type":"post"},{"type":"review"},{"type":"product"}]
   */
  @CacheResponse(180, { withBody: true })
  async list({ request, response }: HttpContext) {
    const { limit = 10, miles, latitude, longitude } = request.qs()

    const data = request.body()

    const payload = await newsfeedValidator.validate(data)

    const cleanIds = payload.filterIds?.filter((id: string) => !!id) || []

    let requestUserId
    const authToken = request.header('Authorization') as string
    JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
      if (decodedToken) {
        requestUserId = decodedToken.userId
      }
    })

    let user
    if (requestUserId) {
      user = await ZnUser.find(requestUserId)
    }

    const lat = Number.parseFloat(latitude || user?.latitude)
    const lon = Number.parseFloat(longitude || user?.longitude)
    const maxDistance = Number.parseFloat(miles || user?.mile) || 10

    const newsfeedService = new NewsfeedService()

    try {
      const newsfeed = await newsfeedService.fetch({
        userId: requestUserId,
        limit,
        filterIds: cleanIds,
        locale: payload.locale,
        lat,
        lon,
        maxDistance,
      })

      return response.ok(newsfeed)
    } catch (error) {
      console.log(error)

      return response.internalServerError(error)
    }
  }

  async getReels({ request, response, user, locale }: HttpContext) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        categoryIds,
        storeIds,
        latitude,
        longitude,
        miles = 50,
        allCountry = false,
        sortBy = 'newest',
      } = request.qs()

      const query = ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias', (mediaQuery) => {
          mediaQuery.where('type', MEDIA_TYPE.VIDEO).orderByRaw('RAND()')
        })
        .preload('thumbnail')
        .preload('user', (query) => {
          query.preload('affiliate')
        })
        .preload('product', (productQuery) => {
          productQuery.preload('variant').preload('image').where('status', 'active')
        })
        .preload('timelines', (timelineQuery) => {
          timelineQuery
            .whereHas('variant', (variantQuery) => {
              variantQuery.whereHas('product', (productQuery) => {
                productQuery.where('status', 'active')
              })
            })
            .preload('variant', (variantQuery) => {
              variantQuery.preload('image').preload('product')
            })
        })
        .preload('stream')
        .whereNull('deletedAt')
        .where('expired', false)
        .where('isDraft', false)
        .where('source', EPostSource.REEL)
        .where((videoQuery) => {
          videoQuery.orWhere('type', EPostType.VIDEO).orWhere((nestedQuery) => {
            nestedQuery.whereNull('type').whereHas('medias', (mediaQuery) => {
              mediaQuery.where('type', MEDIA_TYPE.VIDEO)
            })
          })
        })

      let filteredIds: string[] = []
      if (search || (latitude && longitude)) {
        const lat = Number.parseFloat(latitude)
        const lon = Number.parseFloat(longitude)
        const maxDistance = Number.parseFloat(miles)

        if (search && lat && lon && !allCountry) {
          filteredIds = await this.postService.searchByTextWithinLocation({
            search,
            lat,
            lon,
            miles: maxDistance,
            allCountry,
          })
        } else if (search && lat && lon && allCountry) {
          const searchIds = await this.postService.searchByText(search)
          const locationIds = await this.postService.getPostIdsWithinDistance({
            lat,
            lon,
            miles: maxDistance,
          })
          filteredIds = searchIds.filter((id) => locationIds.includes(id))
        } else if (search) {
          filteredIds = await this.postService.searchByText(search)
        } else if (lat && lon) {
          filteredIds = await this.postService.getPostIdsWithinDistance({
            lat,
            lon,
            miles: maxDistance,
          })
        }

        console.log(`🔍 Search returned ${filteredIds.length} post IDs:`, filteredIds.slice(0, 5))
        if (filteredIds.length > 0) {
          query.whereIn('id', filteredIds)
        } else {
          console.log('❌ No filtered IDs, setting query to return no results')
          query.whereRaw('1=0')
        }
      }

      if (categoryIds && categoryIds.length > 0) {
        const categoryIdsArray = Array.isArray(categoryIds) ? categoryIds : [categoryIds]
        query.whereHas('categories', (categoryQuery) => {
          categoryQuery.whereIn('zn_post_categories.id', categoryIdsArray)
        })
      }

      if (storeIds && storeIds.length > 0) {
        const storeIdsArray = Array.isArray(storeIds) ? storeIds : [storeIds]
        query.whereHas('store', (storeQuery) => {
          storeQuery.whereIn('zn_stores.id', storeIdsArray)
        })
      }

      if (!search && latitude && longitude) {
        const lat = Number.parseFloat(latitude)
        const lon = Number.parseFloat(longitude)
        const maxDistance = Number.parseFloat(miles)

        if (!isNaN(lat) && !isNaN(lon) && !isNaN(maxDistance)) {
          query.whereRaw(
            `(
              3959 * acos(
                cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) +
                sin(radians(?)) * sin(radians(latitude))
              )
            ) <= ?`,
            [lat, lon, lat, maxDistance]
          )
        }
      }

      // Sorting
      if (sortBy === 'newest') {
        query.orderBy('createdAt', 'desc')
      } else if (sortBy === 'popular') {
        query.orderBy('createdAt', 'desc')
      } else {
        query.orderByRaw('RAND()')
      }

      const result = await query.paginate(page, limit)
      console.log(`📊 Final query returned ${result.all().length} posts out of ${result.total} total`)

      const data = await this.translationService.modelsWithPagination(
        TranslationModelName.post,
        result,
        locale
      )

      data.data = await Promise.all(
        data.data.map(async (post) => {
          // Get first video only
          post.medias = post.medias.filter((m: any) => m.type === MEDIA_TYPE.VIDEO).slice(0, 1)

          // Like and comment counts
          const [likeCount, commentCount, isLike] = await Promise.all([
            db
              .from('zn_users_like_resources')
              .where({ resourceType: RESOURCE_TYPE.POST, resourceId: post.id })
              .count('* as total')
              .then((r) => Number(r[0].total)),

            db
              .from('zn_post_comments')
              .where({ postId: post.id })
              .count('* as total')
              .then((r) => Number(r[0].total)),

            this.postService.isUserLikePost(post.id, user?.id),
          ])

          return {
            ...post,
            likeCount,
            commentCount,
            isFavourite: isLike,
          }
        })
      )

      return response.ok(data)
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
